#!/usr/bin/env python3
"""
Complete T-Office Application Verification Script
Tests all components, routes, static files, and functionality
"""

import sys
import os
import requests
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

class TOfficeVerifier:
    def __init__(self):
        self.base_url = "http://127.0.0.1:5001"
        self.results = {
            'server_status': False,
            'routes': {},
            'static_files': {},
            'templates': {},
            'database': False,
            'api_endpoints': {},
            'overall_score': 0
        }
        
    def verify_server_status(self):
        """Check if Flask server is running"""
        try:
            response = requests.get(self.base_url, timeout=5)
            self.results['server_status'] = response.status_code == 200
            print(f"✅ Server Status: {'Running' if self.results['server_status'] else 'Not Running'}")
            return self.results['server_status']
        except requests.exceptions.RequestException as e:
            print(f"❌ Server Status: Not Running - {str(e)}")
            return False
    
    def verify_static_files(self):
        """Verify all static files are accessible"""
        static_files = [
            '/static/css/main.css',
            '/static/css/components.css', 
            '/static/css/responsive.css',
            '/static/js/main.js',
            '/static/js/voice_search.js',
            '/static/js/analytics.js',
            '/static/js/service-worker.js',
            '/static/manifest.json'
        ]
        
        print("\n🔍 Verifying Static Files:")
        print("-" * 50)
        
        for file_path in static_files:
            try:
                response = requests.get(f"{self.base_url}{file_path}", timeout=5)
                success = response.status_code == 200
                self.results['static_files'][file_path] = success
                status = "✅" if success else "❌"
                print(f"{status} {file_path}")
            except Exception as e:
                self.results['static_files'][file_path] = False
                print(f"❌ {file_path} - Error: {str(e)}")
    
    def verify_main_routes(self):
        """Verify main application routes"""
        routes = [
            ('/', 'Home Page'),
            ('/login', 'Login Page'),
            ('/dashboard', 'Dashboard (requires auth)'),
            ('/scan', 'QR Scanner (requires auth)'),
            ('/analytics', 'Analytics (requires auth)'),
            ('/collaboration', 'Collaboration (requires auth)'),
            ('/compartment-qr', 'Compartment QR (requires auth)'),
            ('/compartment-search', 'Compartment Search (requires auth)')
        ]
        
        print("\n🔍 Verifying Main Routes:")
        print("-" * 50)
        
        for route, description in routes:
            try:
                response = requests.get(f"{self.base_url}{route}", timeout=5, allow_redirects=False)
                # Accept 200 (accessible) or 302 (redirect to login) as success
                success = response.status_code in [200, 302]
                self.results['routes'][route] = success
                status = "✅" if success else "❌"
                redirect_info = f" (→ {response.headers.get('Location', '')})" if response.status_code == 302 else ""
                print(f"{status} {route} - {description}{redirect_info}")
            except Exception as e:
                self.results['routes'][route] = False
                print(f"❌ {route} - Error: {str(e)}")
    
    def verify_api_endpoints(self):
        """Verify API endpoints"""
        api_endpoints = [
            ('/api/analytics', 'Analytics API'),
            ('/api/search?q=test', 'Search API (requires auth)'),
            ('/api/files', 'Files API (requires auth)'),
            ('/api/bundles', 'Bundles API (requires auth)'),
            ('/api/compartments', 'Compartments API (requires auth)'),
            ('/api/analytics/dashboard', 'Dashboard Analytics API (requires auth)')
        ]
        
        print("\n🔍 Verifying API Endpoints:")
        print("-" * 50)
        
        for endpoint, description in api_endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=5)
                # Accept various status codes as they may require authentication
                success = response.status_code in [200, 302, 401, 403]
                self.results['api_endpoints'][endpoint] = success
                status = "✅" if success else "❌"
                print(f"{status} {endpoint} - {description} (Status: {response.status_code})")
            except Exception as e:
                self.results['api_endpoints'][endpoint] = False
                print(f"❌ {endpoint} - Error: {str(e)}")
    
    def verify_file_structure(self):
        """Verify critical file structure"""
        critical_files = [
            'app.py',
            'config.py',
            'requirements.txt',
            'templates/base.html',
            'templates/dashboard.html',
            'templates/login.html',
            'static/css/main.css',
            'static/js/main.js',
            'models/user.py',
            'models/file.py'
        ]
        
        print("\n🔍 Verifying File Structure:")
        print("-" * 50)
        
        for file_path in critical_files:
            full_path = project_root / file_path
            exists = full_path.exists()
            status = "✅" if exists else "❌"
            print(f"{status} {file_path}")
    
    def calculate_overall_score(self):
        """Calculate overall application health score"""
        total_checks = 0
        passed_checks = 0
        
        # Server status (critical)
        total_checks += 1
        if self.results['server_status']:
            passed_checks += 1
        
        # Static files
        for success in self.results['static_files'].values():
            total_checks += 1
            if success:
                passed_checks += 1
        
        # Routes
        for success in self.results['routes'].values():
            total_checks += 1
            if success:
                passed_checks += 1
        
        # API endpoints
        for success in self.results['api_endpoints'].values():
            total_checks += 1
            if success:
                passed_checks += 1
        
        score = (passed_checks / total_checks) * 100 if total_checks > 0 else 0
        self.results['overall_score'] = score
        return score
    
    def run_complete_verification(self):
        """Run all verification tests"""
        print("🚀 T-Office Complete Application Verification")
        print("=" * 60)
        
        # Check if server is running first
        if not self.verify_server_status():
            print("\n❌ Server is not running. Please start the Flask application first.")
            print("Run: python app.py")
            return False
        
        # Run all verification tests
        self.verify_static_files()
        self.verify_main_routes()
        self.verify_api_endpoints()
        self.verify_file_structure()
        
        # Calculate and display results
        score = self.calculate_overall_score()
        
        print("\n" + "=" * 60)
        print("📊 VERIFICATION SUMMARY")
        print("=" * 60)
        
        print(f"🎯 Overall Health Score: {score:.1f}%")
        
        if score >= 90:
            print("🟢 EXCELLENT: Application is fully functional!")
        elif score >= 75:
            print("🟡 GOOD: Application is mostly functional with minor issues")
        elif score >= 50:
            print("🟠 FAIR: Application has some functionality but needs attention")
        else:
            print("🔴 POOR: Application has significant issues that need to be resolved")
        
        print(f"\n✅ Server Running: {self.results['server_status']}")
        print(f"✅ Static Files: {sum(self.results['static_files'].values())}/{len(self.results['static_files'])}")
        print(f"✅ Routes Working: {sum(self.results['routes'].values())}/{len(self.results['routes'])}")
        print(f"✅ API Endpoints: {sum(self.results['api_endpoints'].values())}/{len(self.results['api_endpoints'])}")
        
        return score >= 75

if __name__ == "__main__":
    verifier = TOfficeVerifier()
    success = verifier.run_complete_verification()
    
    if success:
        print("\n🎉 T-Office application is ready for use!")
        print("🌐 Access the application at: http://127.0.0.1:5001")
        print("👤 Default login credentials:")
        print("   - Administrator: admin/admin123")
        print("   - Officer: officer/officer123") 
        print("   - Clerk: clerk/clerk123")
    else:
        print("\n⚠️  Some issues were found. Please review the results above.")
    
    sys.exit(0 if success else 1)
